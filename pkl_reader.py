import pickle
import numpy as np
import os
import argparse
from typing import Dict, Any, List, Optional
import matplotlib.pyplot as plt
from pathlib import Path
import json


class PKLReader:
    """
    A comprehensive reader for .pkl files generated by parse_h5_to_pkl_multi.py
    """
    
    def __init__(self, base_path: str):
        """
        Initialize the PKL reader
        
        Args:
            base_path: Base directory containing the pkl data (e.g., "data/pkl_data")
        """
        self.base_path = Path(base_path)
        self.available_tasks = self._discover_tasks()
        
    def _discover_tasks(self) -> List[str]:
        """Discover available tasks in the base directory"""
        tasks = set()
        if self.base_path.exists():
            for item in self.base_path.iterdir():
                if item.is_dir():
                    # Extract task name from directory names like "task_name_Agent0" or "task_name_global"
                    if "_Agent" in item.name:
                        task_name = item.name.split("_Agent")[0]
                        tasks.add(task_name)
                    elif "_global" in item.name:
                        task_name = item.name.split("_global")[0]
                        tasks.add(task_name)
        return list(tasks)
    
    def load_single_step(self, task_name: str, agent_id: Optional[int], episode_id: int, step_id: int) -> Dict[str, Any]:
        """
        Load a single step data
        
        Args:
            task_name: Name of the task
            agent_id: Agent ID (None for global view)
            episode_id: Episode ID
            step_id: Step ID
            
        Returns:
            Dictionary containing the step data
        """
        if agent_id is None:
            dir_name = f"{task_name}_global"
        else:
            dir_name = f"{task_name}_Agent{agent_id}"
            
        file_path = self.base_path / dir_name / f"episode{episode_id}" / f"{step_id}.pkl"
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
            
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
            
        return data
    
    def analyze_data_structure(self, data: Dict[str, Any], indent: int = 0) -> str:
        """
        Recursively analyze the structure of the data
        
        Args:
            data: Data to analyze
            indent: Current indentation level
            
        Returns:
            String representation of the data structure
        """
        result = []
        prefix = "  " * indent
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, np.ndarray):
                    result.append(f"{prefix}{key}: numpy.ndarray, shape={value.shape}, dtype={value.dtype}")
                elif isinstance(value, dict):
                    result.append(f"{prefix}{key}: dict")
                    result.append(self.analyze_data_structure(value, indent + 1))
                elif isinstance(value, list):
                    result.append(f"{prefix}{key}: list, length={len(value)}")
                    if len(value) > 0:
                        result.append(f"{prefix}  Sample element type: {type(value[0])}")
                elif value is None:
                    result.append(f"{prefix}{key}: None")
                else:
                    result.append(f"{prefix}{key}: {type(value).__name__}, value={value}")
        
        return "\n".join(result)
    
    def get_episode_info(self, task_name: str, agent_id: Optional[int], episode_id: int) -> Dict[str, Any]:
        """
        Get information about an entire episode
        
        Args:
            task_name: Name of the task
            agent_id: Agent ID (None for global view)
            episode_id: Episode ID
            
        Returns:
            Dictionary containing episode information
        """
        if agent_id is None:
            dir_name = f"{task_name}_global"
        else:
            dir_name = f"{task_name}_Agent{agent_id}"
            
        episode_dir = self.base_path / dir_name / f"episode{episode_id}"
        
        if not episode_dir.exists():
            raise FileNotFoundError(f"Episode directory not found: {episode_dir}")
        
        # Count steps in the episode
        pkl_files = list(episode_dir.glob("*.pkl"))
        step_count = len(pkl_files)
        
        # Load first and last step for analysis
        first_step = None
        last_step = None
        
        if step_count > 0:
            step_numbers = sorted([int(f.stem) for f in pkl_files])
            first_step = self.load_single_step(task_name, agent_id, episode_id, step_numbers[0])
            if len(step_numbers) > 1:
                last_step = self.load_single_step(task_name, agent_id, episode_id, step_numbers[-1])
        
        return {
            "episode_id": episode_id,
            "step_count": step_count,
            "step_numbers": sorted([int(f.stem) for f in pkl_files]),
            "first_step": first_step,
            "last_step": last_step
        }
    
    def visualize_rgb_image(self, data: Dict[str, Any], save_path: Optional[str] = None):
        """
        Visualize RGB image from the observation data
        
        Args:
            data: Step data containing observation
            save_path: Optional path to save the image
        """
        try:
            rgb_image = data['observation']['head_camera']['rgb']
            
            plt.figure(figsize=(10, 8))
            
            # Handle different image formats
            if rgb_image.dtype == np.uint16:
                # Convert uint16 to uint8 for display
                rgb_image = (rgb_image / 256).astype(np.uint8)
            elif rgb_image.dtype == np.float32 or rgb_image.dtype == np.float64:
                # Normalize float images to [0, 1] range
                rgb_image = np.clip(rgb_image, 0, 1)
            
            # Handle different channel orders
            if rgb_image.shape[0] == 3:  # (3, H, W) format
                rgb_image = np.transpose(rgb_image, (1, 2, 0))
            
            plt.imshow(rgb_image)
            plt.title("RGB Camera Image")
            plt.axis('off')
            
            if save_path:
                plt.savefig(save_path, bbox_inches='tight', dpi=150)
                print(f"Image saved to: {save_path}")
            else:
                plt.show()
                
        except KeyError as e:
            print(f"Error: Could not find RGB image data. Missing key: {e}")
        except Exception as e:
            print(f"Error visualizing image: {e}")
    
    def print_summary(self):
        """Print a summary of available data"""
        print("=" * 60)
        print("PKL Data Reader Summary")
        print("=" * 60)
        print(f"Base path: {self.base_path}")
        print(f"Available tasks: {self.available_tasks}")
        
        for task in self.available_tasks:
            print(f"\nTask: {task}")
            
            # Check available agents and episodes
            task_dirs = list(self.base_path.glob(f"{task}_*"))
            
            for task_dir in task_dirs:
                if task_dir.is_dir():
                    episodes = list(task_dir.glob("episode*"))
                    print(f"  {task_dir.name}: {len(episodes)} episodes")
    
    def export_episode_summary(self, task_name: str, agent_id: Optional[int], episode_id: int, 
                              output_file: str):
        """
        Export episode summary to JSON file
        
        Args:
            task_name: Name of the task
            agent_id: Agent ID (None for global view)
            episode_id: Episode ID
            output_file: Output JSON file path
        """
        try:
            episode_info = self.get_episode_info(task_name, agent_id, episode_id)
            
            # Convert numpy arrays to lists for JSON serialization
            def convert_numpy(obj):
                if isinstance(obj, np.ndarray):
                    return {
                        "type": "numpy_array",
                        "shape": obj.shape,
                        "dtype": str(obj.dtype),
                        "data": obj.tolist() if obj.size < 100 else "too_large_to_serialize"
                    }
                elif isinstance(obj, dict):
                    return {k: convert_numpy(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy(item) for item in obj]
                else:
                    return obj
            
            serializable_info = convert_numpy(episode_info)
            
            with open(output_file, 'w') as f:
                json.dump(serializable_info, f, indent=2)
                
            print(f"Episode summary exported to: {output_file}")
            
        except Exception as e:
            print(f"Error exporting episode summary: {e}")

    def compare_steps(self, task_name: str, agent_id: Optional[int], episode_id: int,
                     step_ids: List[int]):
        """
        Compare multiple steps from the same episode

        Args:
            task_name: Name of the task
            agent_id: Agent ID (None for global view)
            episode_id: Episode ID
            step_ids: List of step IDs to compare
        """
        print(f"\nComparing steps {step_ids} from episode {episode_id}")
        print("=" * 60)

        for step_id in step_ids:
            try:
                data = self.load_single_step(task_name, agent_id, episode_id, step_id)
                print(f"\nStep {step_id}:")
                print("-" * 30)

                # Print action information
                if data['joint_action'] is not None:
                    action = data['joint_action']
                    print(f"  Action shape: {action.shape}")
                    print(f"  Action range: [{np.min(action):.4f}, {np.max(action):.4f}]")
                    print(f"  Action mean: {np.mean(action):.4f}")
                else:
                    print("  No action data (global view)")

                # Print image information
                if 'observation' in data and 'head_camera' in data['observation']:
                    rgb = data['observation']['head_camera']['rgb']
                    print(f"  RGB shape: {rgb.shape}")
                    print(f"  RGB dtype: {rgb.dtype}")
                    print(f"  RGB range: [{np.min(rgb)}, {np.max(rgb)}]")

            except Exception as e:
                print(f"  Error loading step {step_id}: {e}")

    def batch_analyze_episode(self, task_name: str, agent_id: Optional[int], episode_id: int):
        """
        Perform batch analysis on an entire episode

        Args:
            task_name: Name of the task
            agent_id: Agent ID (None for global view)
            episode_id: Episode ID
        """
        try:
            episode_info = self.get_episode_info(task_name, agent_id, episode_id)
            step_numbers = episode_info['step_numbers']

            print(f"\nBatch analysis for episode {episode_id}")
            print("=" * 60)
            print(f"Total steps: {len(step_numbers)}")

            # Analyze action statistics
            if agent_id is not None:  # Only for agent data, not global
                actions = []
                for step_id in step_numbers:
                    try:
                        data = self.load_single_step(task_name, agent_id, episode_id, step_id)
                        if data['joint_action'] is not None:
                            actions.append(data['joint_action'])
                    except:
                        continue

                if actions:
                    actions = np.array(actions)
                    print(f"\nAction Statistics:")
                    print(f"  Shape: {actions.shape}")
                    print(f"  Mean: {np.mean(actions, axis=0)}")
                    print(f"  Std: {np.std(actions, axis=0)}")
                    print(f"  Min: {np.min(actions, axis=0)}")
                    print(f"  Max: {np.max(actions, axis=0)}")

            # Analyze image statistics
            rgb_shapes = []
            rgb_dtypes = []
            for step_id in step_numbers[:10]:  # Sample first 10 steps
                try:
                    data = self.load_single_step(task_name, agent_id, episode_id, step_id)
                    if 'observation' in data and 'head_camera' in data['observation']:
                        rgb = data['observation']['head_camera']['rgb']
                        rgb_shapes.append(rgb.shape)
                        rgb_dtypes.append(str(rgb.dtype))
                except:
                    continue

            if rgb_shapes:
                print(f"\nImage Statistics (sampled from first 10 steps):")
                print(f"  Common shape: {rgb_shapes[0] if rgb_shapes else 'N/A'}")
                print(f"  Common dtype: {rgb_dtypes[0] if rgb_dtypes else 'N/A'}")
                print(f"  Shape consistency: {len(set(rgb_shapes)) == 1}")
                print(f"  Dtype consistency: {len(set(rgb_dtypes)) == 1}")

        except Exception as e:
            print(f"Error in batch analysis: {e}")

    def create_action_trajectory_plot(self, task_name: str, agent_id: int, episode_id: int,
                                    save_path: Optional[str] = None):
        """
        Create a plot showing the action trajectory over time

        Args:
            task_name: Name of the task
            agent_id: Agent ID
            episode_id: Episode ID
            save_path: Optional path to save the plot
        """
        try:
            episode_info = self.get_episode_info(task_name, agent_id, episode_id)
            step_numbers = episode_info['step_numbers']

            actions = []
            valid_steps = []

            for step_id in step_numbers:
                try:
                    data = self.load_single_step(task_name, agent_id, episode_id, step_id)
                    if data['joint_action'] is not None:
                        actions.append(data['joint_action'])
                        valid_steps.append(step_id)
                except:
                    continue

            if not actions:
                print("No action data found for plotting")
                return

            actions = np.array(actions)

            plt.figure(figsize=(12, 8))

            # Plot each joint/action dimension
            for i in range(actions.shape[1]):
                plt.subplot(2, (actions.shape[1] + 1) // 2, i + 1)
                plt.plot(valid_steps, actions[:, i])
                plt.title(f'Joint/Action {i}')
                plt.xlabel('Step')
                plt.ylabel('Value')
                plt.grid(True)

            plt.tight_layout()
            plt.suptitle(f'Action Trajectory - Task: {task_name}, Agent: {agent_id}, Episode: {episode_id}')

            if save_path:
                plt.savefig(save_path, bbox_inches='tight', dpi=150)
                print(f"Action trajectory plot saved to: {save_path}")
            else:
                plt.show()

        except Exception as e:
            print(f"Error creating action trajectory plot: {e}")


def main():
    parser = argparse.ArgumentParser(description="Read and analyze PKL files")
    parser.add_argument("--base_path", type=str, default="data/pkl_data", 
                       help="Base path to PKL data directory")
    parser.add_argument("--task_name", type=str, help="Task name to analyze")
    parser.add_argument("--agent_id", type=int, help="Agent ID (omit for global view)")
    parser.add_argument("--episode_id", type=int, default=0, help="Episode ID")
    parser.add_argument("--step_id", type=int, default=0, help="Step ID")
    parser.add_argument("--action", type=str, choices=["summary", "analyze", "visualize", "export", "compare", "batch", "plot"],
                       default="summary", help="Action to perform")
    parser.add_argument("--output", type=str, help="Output file path (for export action)")
    parser.add_argument("--step_ids", type=str, help="Comma-separated step IDs for compare action (e.g., '0,5,10')")

    args = parser.parse_args()
    
    # Initialize reader
    reader = PKLReader(args.base_path)
    
    if args.action == "summary":
        reader.print_summary()
        
    elif args.action == "analyze":
        if not args.task_name:
            print("Error: --task_name is required for analyze action")
            return
            
        try:
            data = reader.load_single_step(args.task_name, args.agent_id, args.episode_id, args.step_id)
            print(f"\nAnalyzing step {args.step_id} of episode {args.episode_id}")
            print("=" * 60)
            print(reader.analyze_data_structure(data))
            
        except Exception as e:
            print(f"Error: {e}")
            
    elif args.action == "visualize":
        if not args.task_name:
            print("Error: --task_name is required for visualize action")
            return
            
        try:
            data = reader.load_single_step(args.task_name, args.agent_id, args.episode_id, args.step_id)
            reader.visualize_rgb_image(data, args.output)
            
        except Exception as e:
            print(f"Error: {e}")
            
    elif args.action == "export":
        if not args.task_name or not args.output:
            print("Error: --task_name and --output are required for export action")
            return
            
        try:
            reader.export_episode_summary(args.task_name, args.agent_id, args.episode_id, args.output)
            
        except Exception as e:
            print(f"Error: {e}")

    elif args.action == "compare":
        if not args.task_name or not args.step_ids:
            print("Error: --task_name and --step_ids are required for compare action")
            return

        try:
            step_ids = [int(x.strip()) for x in args.step_ids.split(',')]
            reader.compare_steps(args.task_name, args.agent_id, args.episode_id, step_ids)

        except Exception as e:
            print(f"Error: {e}")

    elif args.action == "batch":
        if not args.task_name:
            print("Error: --task_name is required for batch action")
            return

        try:
            reader.batch_analyze_episode(args.task_name, args.agent_id, args.episode_id)

        except Exception as e:
            print(f"Error: {e}")

    elif args.action == "plot":
        if not args.task_name or args.agent_id is None:
            print("Error: --task_name and --agent_id are required for plot action")
            return

        try:
            reader.create_action_trajectory_plot(args.task_name, args.agent_id, args.episode_id, args.output)

        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    main()
