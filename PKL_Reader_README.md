# PKL Reader - 用于读取和分析 .pkl 文件的工具

这个工具用于读取和分析由 `parse_h5_to_pkl_multi.py` 生成的 `.pkl` 文件。它提供了多种功能来查看、分析和可视化机器人轨迹数据。

## 功能特性

- 📁 **数据发现**: 自动发现可用的任务和智能体数据
- 🔍 **结构分析**: 深入分析数据结构和内容
- 📊 **统计分析**: 计算动作和图像的统计信息
- 📈 **可视化**: 生成RGB图像和动作轨迹图表
- 🔄 **批量处理**: 批量分析整个轨迹
- 📋 **数据导出**: 导出分析结果为JSON格式
- 🤖 **多智能体支持**: 支持多智能体数据分析和比较

## 安装依赖

```bash
pip install numpy matplotlib pickle pathlib argparse tqdm
```

## 文件结构

生成的 `.pkl` 文件应该按以下结构组织：

```
data/pkl_data/
├── {task_name}_Agent0/
│   ├── episode0/
│   │   ├── 0.pkl
│   │   ├── 1.pkl
│   │   └── ...
│   └── episode1/
├── {task_name}_Agent1/
├── {task_name}_global/
└── ...
```

## 使用方法

### 1. 命令行使用

#### 查看数据摘要
```bash
python pkl_reader.py --action summary
```

#### 分析单个步骤
```bash
python pkl_reader.py --action analyze --task_name "pick_and_place" --agent_id 0 --episode_id 0 --step_id 0
```

#### 可视化RGB图像
```bash
python pkl_reader.py --action visualize --task_name "pick_and_place" --agent_id 0 --episode_id 0 --step_id 0 --output "image.png"
```

#### 比较多个步骤
```bash
python pkl_reader.py --action compare --task_name "pick_and_place" --agent_id 0 --episode_id 0 --step_ids "0,5,10"
```

#### 批量分析整个轨迹
```bash
python pkl_reader.py --action batch --task_name "pick_and_place" --agent_id 0 --episode_id 0
```

#### 生成动作轨迹图
```bash
python pkl_reader.py --action plot --task_name "pick_and_place" --agent_id 0 --episode_id 0 --output "trajectory.png"
```

#### 导出轨迹摘要
```bash
python pkl_reader.py --action export --task_name "pick_and_place" --agent_id 0 --episode_id 0 --output "summary.json"
```

### 2. 编程使用

```python
from pkl_reader import PKLReader

# 初始化读取器
reader = PKLReader("data/pkl_data")

# 加载单个步骤数据
data = reader.load_single_step("pick_and_place", agent_id=0, episode_id=0, step_id=0)

# 分析数据结构
structure = reader.analyze_data_structure(data)
print(structure)

# 获取轨迹信息
episode_info = reader.get_episode_info("pick_and_place", agent_id=0, episode_id=0)
print(f"Total steps: {episode_info['step_count']}")

# 可视化RGB图像
reader.visualize_rgb_image(data, save_path="output.png")
```

## 数据格式说明

每个 `.pkl` 文件包含一个字典，结构如下：

```python
{
    "pointcloud": None,                    # 点云数据（当前未使用）
    "joint_action": numpy.ndarray,         # 关节动作（智能体数据）或 None（全局视角）
    "endpose": numpy.ndarray,              # 末端执行器姿态（智能体数据）或 None（全局视角）
    "observation": {                       # 观察数据
        "head_camera": {
            "rgb": numpy.ndarray,          # RGB图像 (H, W, 3)
            "intrinsic_cv": numpy.ndarray, # 相机内参矩阵 (3, 3)
            "extrinsic_cv": numpy.ndarray, # 相机外参矩阵 (4, 4)
            "cam2world_gl": numpy.ndarray  # 相机到世界坐标变换 (4, 4)
        }
    }
}
```

## 命令行参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--base_path` | str | "data/pkl_data" | PKL数据目录的基础路径 |
| `--task_name` | str | None | 要分析的任务名称 |
| `--agent_id` | int | None | 智能体ID（省略表示全局视角） |
| `--episode_id` | int | 0 | 轨迹ID |
| `--step_id` | int | 0 | 步骤ID |
| `--action` | str | "summary" | 要执行的操作 |
| `--output` | str | None | 输出文件路径 |
| `--step_ids` | str | None | 逗号分隔的步骤ID列表（用于比较） |

## 支持的操作

| 操作 | 说明 | 必需参数 |
|------|------|----------|
| `summary` | 显示数据摘要 | 无 |
| `analyze` | 分析单个步骤的数据结构 | `task_name` |
| `visualize` | 可视化RGB图像 | `task_name` |
| `export` | 导出轨迹摘要为JSON | `task_name`, `output` |
| `compare` | 比较多个步骤 | `task_name`, `step_ids` |
| `batch` | 批量分析整个轨迹 | `task_name` |
| `plot` | 生成动作轨迹图 | `task_name`, `agent_id` |

## 示例输出

### 数据结构分析
```
pointcloud: None
joint_action: numpy.ndarray, shape=(7,), dtype=float64
endpose: numpy.ndarray, shape=(7,), dtype=float64
observation: dict
  head_camera: dict
    rgb: numpy.ndarray, shape=(480, 640, 3), dtype=uint8
    intrinsic_cv: numpy.ndarray, shape=(3, 3), dtype=float64
    extrinsic_cv: numpy.ndarray, shape=(4, 4), dtype=float64
    cam2world_gl: numpy.ndarray, shape=(4, 4), dtype=float64
```

### 批量分析输出
```
Batch analysis for episode 0
============================================================
Total steps: 150

Action Statistics:
  Shape: (150, 7)
  Mean: [ 0.1234 -0.5678  0.9012 ...]
  Std: [0.2345  0.1234  0.3456 ...]
  Min: [-1.2345 -2.3456 -0.5678 ...]
  Max: [ 1.5678  0.8901  1.2345 ...]

Image Statistics (sampled from first 10 steps):
  Common shape: (480, 640, 3)
  Common dtype: uint8
  Shape consistency: True
  Dtype consistency: True
```

## 故障排除

1. **FileNotFoundError**: 确保PKL文件路径正确，并且已经运行了 `parse_h5_to_pkl_multi.py`
2. **KeyError**: 检查数据结构是否符合预期格式
3. **ImportError**: 确保安装了所有必需的依赖包
4. **matplotlib显示问题**: 在服务器环境中使用 `--output` 参数保存图像而不是显示

## 扩展功能

可以通过继承 `PKLReader` 类来添加自定义分析功能：

```python
class CustomPKLReader(PKLReader):
    def custom_analysis(self, data):
        # 添加自定义分析逻辑
        pass
```

## 性能优化

- 对于大型数据集，使用批量分析功能而不是逐步加载
- 图像可视化时指定输出路径以避免显示开销
- 使用采样分析来快速了解数据特征
