#!/usr/bin/env python3
"""
Example usage of the PKL Reader for analyzing .pkl files generated by parse_h5_to_pkl_multi.py

This script demonstrates how to use the PKLReader class programmatically.
"""

from pkl_reader import PKLReader
import numpy as np


def example_usage():
    """Demonstrate various ways to use the PKL Reader"""
    
    # Initialize the reader
    reader = PKLReader("data/pkl_data")
    
    print("=== PKL Reader Example Usage ===\n")
    
    # 1. Print summary of available data
    print("1. Data Summary:")
    reader.print_summary()
    
    # Check if we have any tasks available
    if not reader.available_tasks:
        print("\nNo tasks found in the data directory.")
        print("Make sure you have run parse_h5_to_pkl_multi.py first to generate .pkl files.")
        return
    
    # Use the first available task for demonstration
    task_name = reader.available_tasks[0]
    print(f"\nUsing task '{task_name}' for demonstration...\n")
    
    try:
        # 2. Load and analyze a single step
        print("2. Single Step Analysis:")
        print("-" * 40)
        data = reader.load_single_step(task_name, agent_id=0, episode_id=0, step_id=0)
        print("Data structure:")
        print(reader.analyze_data_structure(data))
        
        # 3. Get episode information
        print("\n3. Episode Information:")
        print("-" * 40)
        episode_info = reader.get_episode_info(task_name, agent_id=0, episode_id=0)
        print(f"Episode {episode_info['episode_id']}:")
        print(f"  Total steps: {episode_info['step_count']}")
        print(f"  Step range: {min(episode_info['step_numbers'])} - {max(episode_info['step_numbers'])}")
        
        # 4. Compare multiple steps
        print("\n4. Step Comparison:")
        print("-" * 40)
        if episode_info['step_count'] >= 3:
            step_ids = [0, episode_info['step_count']//2, episode_info['step_count']-1]
            reader.compare_steps(task_name, agent_id=0, episode_id=0, step_ids)
        
        # 5. Batch analysis
        print("\n5. Batch Episode Analysis:")
        print("-" * 40)
        reader.batch_analyze_episode(task_name, agent_id=0, episode_id=0)
        
        # 6. Demonstrate data access patterns
        print("\n6. Data Access Examples:")
        print("-" * 40)
        
        # Access RGB image
        if 'observation' in data and 'head_camera' in data['observation']:
            rgb_image = data['observation']['head_camera']['rgb']
            print(f"RGB Image:")
            print(f"  Shape: {rgb_image.shape}")
            print(f"  Data type: {rgb_image.dtype}")
            print(f"  Value range: [{np.min(rgb_image)}, {np.max(rgb_image)}]")
        
        # Access camera parameters
        if 'observation' in data and 'head_camera' in data['observation']:
            camera_data = data['observation']['head_camera']
            if 'intrinsic_cv' in camera_data:
                intrinsic = camera_data['intrinsic_cv']
                print(f"\nCamera Intrinsic Matrix:")
                print(f"  Shape: {intrinsic.shape}")
                print(f"  Focal length (fx, fy): ({intrinsic[0,0]:.2f}, {intrinsic[1,1]:.2f})")
                print(f"  Principal point (cx, cy): ({intrinsic[0,2]:.2f}, {intrinsic[1,2]:.2f})")
        
        # Access action data
        if data['joint_action'] is not None:
            action = data['joint_action']
            print(f"\nJoint Action:")
            print(f"  Shape: {action.shape}")
            print(f"  Values: {action}")
        else:
            print(f"\nNo action data (this might be global view)")
        
        # 7. Export episode summary
        print("\n7. Exporting Episode Summary:")
        print("-" * 40)
        output_file = f"episode_summary_{task_name}_agent0_ep0.json"
        reader.export_episode_summary(task_name, agent_id=0, episode_id=0, output_file)
        
    except FileNotFoundError as e:
        print(f"File not found: {e}")
        print("Make sure the specified task, agent, and episode exist.")
    except Exception as e:
        print(f"Error during analysis: {e}")


def demonstrate_visualization():
    """Demonstrate visualization capabilities"""
    
    reader = PKLReader("data/pkl_data")
    
    if not reader.available_tasks:
        print("No tasks available for visualization demo.")
        return
    
    task_name = reader.available_tasks[0]
    
    try:
        print("\n=== Visualization Examples ===\n")
        
        # Load a step with image data
        data = reader.load_single_step(task_name, agent_id=0, episode_id=0, step_id=0)
        
        # Visualize RGB image (save to file instead of showing)
        print("1. RGB Image Visualization:")
        reader.visualize_rgb_image(data, save_path=f"rgb_image_{task_name}_step0.png")
        
        # Create action trajectory plot
        print("\n2. Action Trajectory Plot:")
        reader.create_action_trajectory_plot(
            task_name, 
            agent_id=0, 
            episode_id=0, 
            save_path=f"action_trajectory_{task_name}_agent0_ep0.png"
        )
        
    except Exception as e:
        print(f"Error during visualization: {e}")


def demonstrate_multi_agent_analysis():
    """Demonstrate multi-agent data analysis"""
    
    reader = PKLReader("data/pkl_data")
    
    if not reader.available_tasks:
        print("No tasks available for multi-agent demo.")
        return
    
    task_name = reader.available_tasks[0]
    
    print("\n=== Multi-Agent Analysis ===\n")
    
    # Check what agents are available for this task
    task_dirs = list(reader.base_path.glob(f"{task_name}_*"))
    available_agents = []
    has_global = False
    
    for task_dir in task_dirs:
        if "_Agent" in task_dir.name:
            agent_id = int(task_dir.name.split("_Agent")[1])
            available_agents.append(agent_id)
        elif "_global" in task_dir.name:
            has_global = True
    
    print(f"Available agents for task '{task_name}': {sorted(available_agents)}")
    print(f"Global view available: {has_global}")
    
    # Compare the same step across different agents
    if len(available_agents) >= 2:
        print(f"\nComparing step 0 across agents:")
        for agent_id in sorted(available_agents)[:3]:  # Compare first 3 agents
            try:
                data = reader.load_single_step(task_name, agent_id, episode_id=0, step_id=0)
                print(f"\nAgent {agent_id}:")
                if data['joint_action'] is not None:
                    action = data['joint_action']
                    print(f"  Action shape: {action.shape}")
                    print(f"  Action values: {action}")
                else:
                    print("  No action data")
                    
                # Check image data
                if 'observation' in data and 'head_camera' in data['observation']:
                    rgb = data['observation']['head_camera']['rgb']
                    print(f"  RGB shape: {rgb.shape}")
                    
            except Exception as e:
                print(f"  Error loading data for agent {agent_id}: {e}")
    
    # Compare agent view vs global view
    if has_global and available_agents:
        print(f"\nComparing Agent 0 vs Global view:")
        try:
            agent_data = reader.load_single_step(task_name, agent_id=0, episode_id=0, step_id=0)
            global_data = reader.load_single_step(task_name, agent_id=None, episode_id=0, step_id=0)
            
            print("Agent 0:")
            print(f"  Has action: {agent_data['joint_action'] is not None}")
            if 'observation' in agent_data and 'head_camera' in agent_data['observation']:
                print(f"  RGB shape: {agent_data['observation']['head_camera']['rgb'].shape}")
            
            print("Global view:")
            print(f"  Has action: {global_data['joint_action'] is not None}")
            if 'observation' in global_data and 'head_camera' in global_data['observation']:
                print(f"  RGB shape: {global_data['observation']['head_camera']['rgb'].shape}")
                
        except Exception as e:
            print(f"Error comparing agent vs global: {e}")


if __name__ == "__main__":
    # Run all demonstration functions
    example_usage()
    demonstrate_visualization()
    demonstrate_multi_agent_analysis()
    
    print("\n=== Demo Complete ===")
    print("Check the generated files:")
    print("- episode_summary_*.json")
    print("- rgb_image_*.png") 
    print("- action_trajectory_*.png")
