import pickle, os
import numpy as np
import pdb
import zarr
import shutil
import argparse


def main():
    parser = argparse.ArgumentParser(description='Process some episodes.')
    parser.add_argument('--task_name', type=str, default='LiftBarrier',
                        help='The name of the task (e.g., LiftBarrier)')
    parser.add_argument('--agent_id', type=int, default='LiftBarrier',
                        help='The id of the agent (e.g., LiftBarrier)')
    parser.add_argument('--load_num', type=int, default=50,
                        help='Number of episodes to process (e.g., 50)')
    args = parser.parse_args()

    task_name = args.task_name
    agent_id = args.agent_id
    num = args.load_num
    load_dir = f'data/pkl_data/{task_name}_Agent{agent_id}'
    
    total_count = 0
    save_dir = f'data/zarr_data/{task_name}_Agent{agent_id}_{num}.zarr'

    if os.path.exists(save_dir):
        shutil.rmtree(save_dir)

    current_ep = 0

    zarr_root = zarr.group(save_dir)
    zarr_data = zarr_root.create_group('data')
    zarr_meta = zarr_root.create_group('meta')

    # head_camera_arrays = [], []
    # episode_ends_arrays, action_arrays, state_arrays, joint_action_arrays = [], [], [], []
    head_camera_agent_arrays, head_camera_global_arrays = [], []
    episode_ends_arrays, state_arrays, joint_action_arrays = [], [], []
    
    num_files = 454
    while os.path.isdir(load_dir+f'/episode{current_ep}') and current_ep < num:
        print(f'processing episode: {current_ep + 1} / {num}', end='\r')
        file_num = 0
        
        while os.path.exists(load_dir+f'/episode{current_ep}'+f'/{file_num}.pkl') and file_num < num_files:
            with open(load_dir+f'/episode{current_ep}'+f'/{file_num}.pkl', 'rb') as file:
                data = pickle.load(file)

            # head_img = data['observation']['head_camera']['rgb']
            head_img_agent = data["observation"]["head_camera_agent"]["rgb"]
            head_img_global = data["observation"]["head_camera_global"]["rgb"]
            
            joint_state = data['joint_action']

            if file_num + 1 < num_files:
                next_data = pickle.load(open(load_dir+f'/episode{current_ep}'+f'/{file_num + 1}.pkl', 'rb'))
                joint_action = np.array(next_data['joint_state'])
            else:
                joint_action = np.array(data['joint_state'])

            # head_camera_arrays.append(head_img)
            head_camera_agent_arrays.append(head_img_agent)
            head_camera_global_arrays.append(head_img_global)
            state_arrays.append(joint_state)
            joint_action_arrays.append(joint_action)

            file_num += 1
            total_count += 1
            
        current_ep += 1

        episode_ends_arrays.append(total_count)

    print()
    episode_ends_arrays = np.array(episode_ends_arrays)
    head_camera_agent_arrays = np.array(head_camera_agent_arrays)
    head_camera_global_arrays = np.array(head_camera_global_arrays)
    state_arrays = np.array(state_arrays)
    joint_action_arrays = np.array(joint_action_arrays)

    head_camera_agent_arrays = np.moveaxis(head_camera_agent_arrays, -1, 1)  # NHWC -> NCHW
    head_camera_global_arrays = np.moveaxis(head_camera_global_arrays, -1, 1)  # NHWC -> NCHW
    print ("head_camera_agent_arrays.shape", head_camera_agent_arrays.shape)
    print ("head_camera_global_arrays.shape", head_camera_global_arrays.shape)


    compressor = zarr.Blosc(cname='zstd', clevel=3, shuffle=1)
   
    head_camera_agent_chunk_size = (100, *head_camera_agent_arrays.shape[1:])
    head_camera_global_chunk_size = (100, *head_camera_global_arrays.shape[1:])
    state_chunk_size = (100, state_arrays.shape[1])
    joint_chunk_size = (100, joint_action_arrays.shape[1])
    
    zarr_data.create_dataset('head_camera_agent', data=head_camera_agent_arrays, chunks=head_camera_agent_chunk_size, overwrite=True, compressor=compressor)
    zarr_data.create_dataset('head_camera_global', data=head_camera_global_arrays, chunks=head_camera_global_chunk_size, overwrite=True, compressor=compressor)
    zarr_data.create_dataset('state', data=state_arrays, chunks=state_chunk_size, dtype='float32', overwrite=True, compressor=compressor)
    zarr_data.create_dataset('action', data=joint_action_arrays, chunks=joint_chunk_size, dtype='float32', overwrite=True, compressor=compressor)
    zarr_meta.create_dataset('episode_ends', data=episode_ends_arrays, dtype='int64', overwrite=True, compressor=compressor)

if __name__ == '__main__':
    main()