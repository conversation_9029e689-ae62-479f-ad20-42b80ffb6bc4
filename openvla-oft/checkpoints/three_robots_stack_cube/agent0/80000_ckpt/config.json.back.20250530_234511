{"arch_specifier": "no-align+fused-gelu-mlp", "architectures": ["OpenVLAForActionPrediction"], "auto_map": {"AutoConfig": "openvla/openvla-7b--configuration_prismatic.OpenVLAConfig", "AutoModelForVision2Seq": "openvla/openvla-7b--modeling_prismatic.OpenVLAForActionPrediction"}, "hf_llm_id": "meta-llama/Llama-2-7b-hf", "image_resize_strategy": "resize-naive", "image_sizes": [224, 224], "llm_backbone_id": "llama2-7b-pure", "llm_max_length": 2048, "model_type": "openvla", "n_action_bins": 256, "norm_stats": {"austin_buds_dataset_converted_externally_to_rlds": {"action": {"mask": [true, true, true, true, true, true, false], "max": [1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0], "mean": [-0.07678354531526566, 0.0036849044263362885, 0.05644911900162697, 0.0, 0.0, 0.0, 0.3510494828224182], "min": [-1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0], "q01": [-1.0, -0.9599999785423279, -0.8714285492897034, 0.0, 0.0, 0.0, 0.0], "q99": [1.0, 0.8600000143051147, 1.0, 0.0, 0.0, 0.0, 1.0], "std": [0.6367740631103516, 0.37889179587364197, 0.47796326875686646, 0.0, 0.0, 0.0, 0.47721168398857117]}, "num_trajectories": 50, "num_transitions": 34112, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "austin_sailor_dataset_converted_externally_to_rlds": {"action": {"mask": [true, true, true, true, true, true, false], "max": [1.0, 1.0, 1.0, 0.0, 0.0, 0.375, 1.0], "mean": [0.011825348250567913, 0.006461074110120535, 0.06023626774549484, 0.0, 0.0, 0.0016465914668515325, 0.5260950326919556], "min": [-1.0, -1.0, -1.0, 0.0, 0.0, -0.375, 0.0], "q01": [-1.0, -0.9828571677207947, -0.6000000238418579, 0.0, 0.0, -0.17249999940395355, 0.0], "q99": [1.0, 0.9457142949104309, 1.0, 0.0, 0.0, 0.17892856895923615, 1.0], "std": [0.46348899602890015, 0.41240179538726807, 0.411862850189209, 0.0, 0.0, 0.0578610822558403, 0.49894046783447266]}, "num_trajectories": 240, "num_transitions": 353094, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "austin_sirius_dataset_converted_externally_to_rlds": {"action": {"mask": [true, true, true, true, true, true, false], "max": [1.0002285242080688, 0.960608720779419, 1.105179786682129, 0.0, 0.0, 0.341785728931427, 1.0], "mean": [0.07747682929039001, 0.03195561468601227, 0.04244732856750488, 0.0, 0.0, -0.01603456400334835, 0.43260177969932556], "min": [-1.0183025598526, -0.9800000190734863, -0.9774575233459473, 0.0, 0.0, -0.34607142210006714, 0.0], "q01": [-0.780905865430832, -0.5667179036140442, -0.5254343223571777, 0.0, 0.0, -0.28495091378688814, 0.0], "q99": [0.9569637751579284, 0.6971374487876891, 0.8124888157844541, 0.0, 0.0, 0.1971428543329239, 1.0], "std": [0.3906329572200775, 0.2998155355453491, 0.2782271206378937, 0.0, 0.0, 0.08120622485876083, 0.49528297781944275]}, "num_trajectories": 559, "num_transitions": 279939, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "bc_z": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.2165454924106598, 0.1251407265663147, 0.10772687941789627, 0.33544227480888367, 0.28117990493774414, 0.40614867210388184, 1.0], "mean": [-0.009958467446267605, 0.0008958321413956583, 0.004995597992092371, 0.00029755113064311445, -0.008735382929444313, -0.030693737789988518, 0.8344562649726868], "min": [-0.1677047461271286, -0.14630407094955444, -0.10066790133714676, -0.29421567916870117, -0.32101404666900635, -0.4635624885559082, 0.0], "q01": [-0.09220654994249344, -0.06456145539879798, -0.049121275544166565, -0.11594625547528267, -0.14152548640966414, -0.2251061636209488, 0.0], "q99": [0.07628866866230968, 0.058019736707210584, 0.052540797740221024, 0.11740604028105736, 0.11703975558280955, 0.16729306846857078, 1.0], "std": [0.03053455986082554, 0.0231423731893301, 0.020641816779971123, 0.04155943542718887, 0.046427831053733826, 0.0769818127155304, 0.3610210120677948]}, "num_trajectories": 43264, "num_transitions": 6015535, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "berkeley_autolab_ur5": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.019999999552965164, 0.019999999552965164, 0.019999999552965164, 0.06666667014360428, 0.06666667014360428, 0.06666667014360428, 1.0], "mean": [0.0005683620693162084, 0.001217700308188796, -0.0005296372692100704, 0.00021029810886830091, 6.0695128922816366e-05, 0.001204986940138042, 0.6298308372497559], "min": [-0.019999999552965164, -0.019999999552965164, -0.019999999552965164, -0.06666667014360428, -0.06666667014360428, -0.06666667014360428, 0.0], "q01": [-0.019999999552965164, -0.019999999552965164, -0.019999999552965164, -0.02628571353852749, -0.06666667014360428, -0.03847619146108627, 0.0], "q99": [0.019999999552965164, 0.019999999552965164, 0.019999999552965164, 0.031809523701667786, 0.06666667014360428, 0.036571428179740906, 1.0], "std": [0.****************, 0.007990492507815361, 0.009577835910022259, 0.009432995691895485, 0.016427582129836082, 0.011053967289626598, 0.*****************]}, "num_trajectories": 1000, "num_transitions": 97939, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "berkeley_cable_routing": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.****************, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0], "mean": [-0.*****************, 0.023609008640050888, 0.*****************, 0.0, 0.0, 0.049671024084091187, 0.0], "min": [-0.****************, -0.****************, -0.****************, 0.0, 0.0, -1.0, 0.0], "q01": [-0.****************, -0.****************, -0.****************, 0.0, 0.0, -0.****************, 0.0], "q99": [0.*****************, 0.****************, 0.***************, 0.0, 0.0, 0.***************, 0.0], "std": [0.****************, 0.****************, 0.*****************, 0.0, 0.0, 0.****************, 0.0]}, "num_trajectories": 1647, "num_transitions": 42328, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "berkeley_fanuc_manipulation": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.009999999776482582, 0.009999999776482582, 0.009999999776482582, 0.03490658476948738, 0.03490658476948738, 0.03490658476948738, 1.0], "mean": [0.0007744057802483439, -0.00031240080716088414, -0.0015001941937953234, -0.0007515158504247665, -0.00015832878125365824, 0.00014327642566058785, 0.699295699596405], "min": [-0.009999999776482582, -0.009999999776482582, -0.009999999776482582, -0.03490658476948738, -0.03490658476948738, -0.03490658476948738, 0.0], "q01": [-0.009999999776482582, -0.009999999776482582, -0.009999999776482582, -0.03490658476948738, 0.0, -0.03490658476948738, 0.0], "q99": [0.009999999776482582, 0.009999999776482582, 0.009999999776482582, 0.03490658476948738, 0.0, 0.03490658476948738, 1.0], "std": [0.0034070091787725687, 0.0049921851605176926, 0.005344334989786148, 0.00759894959628582, 0.004081866703927517, 0.008568956516683102, 0.4586937427520752]}, "num_trajectories": 415, "num_transitions": 62613, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "bridge_orig": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.41691166162490845, 0.25864794850349426, 0.21218234300613403, 3.122201919555664, 1.8618112802505493, 6.280478477478027, 1.0], "mean": [0.0002334194869035855, 0.00013004911306779832, -0.00012762474943883717, -0.0001556558854645118, -0.0004039328487124294, 0.00023557482927571982, 0.5764579176902771], "min": [-0.4007510244846344, -0.13874775171279907, -0.22553899884223938, -3.2010786533355713, -1.8618112802505493, -6.279075622558594, 0.0], "q01": [-0.02872725307941437, -0.04170349963009357, -0.026093858778476715, -0.08092105075716972, -0.09288699507713317, -0.20718276381492615, 0.0], "q99": [0.028309678435325586, 0.040855254605412394, 0.040161586627364146, 0.08192047759890528, 0.07792850524187081, 0.20382574498653397, 1.0], "std": [0.009765930473804474, 0.013689135201275349, 0.012667362578213215, 0.028534092009067535, 0.030637972056865692, 0.07691419124603271, 0.4973701536655426]}, "num_trajectories": 60064, "num_transitions": 2135463, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "cmu_stretch": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.02338407188653946, 0.0, 0.023404927924275398, 0.0, 0.0, 0.0, 1.0], "mean": [0.00036304505192674696, 0.0, 0.0016466958913952112, 0.0, 0.0, 0.0, 0.3987048268318176], "min": [-0.019353797659277916, 0.0, -0.02019215188920498, 0.0, 0.0, 0.0, 0.0], "q01": [-0.011175686959177256, 0.0, -0.0032206363626755773, 0.0, 0.0, 0.0, 0.0], "q99": [0.014501785952597848, 0.0, 0.015056106168776728, 0.0, 0.0, 0.0, 1.0], "std": [0.004081828519701958, 0.0, 0.0037743328139185905, 0.0, 0.0, 0.0, 0.48963725566864014]}, "num_trajectories": 135, "num_transitions": 25016, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "dlr_edan_shared_control_converted_externally_to_rlds": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.18991442024707794, 0.0739002525806427, 0.18064819276332855, 0.0866486132144928, 0.13464981317520142, 0.16910280287265778, 1.0], "mean": [0.006647810339927673, -0.0007657372043468058, 0.006522852927446365, 0.0011679717572405934, -0.006395625416189432, -0.011902998201549053, 0.6985887289047241], "min": [-0.10054297000169754, -0.08427435159683228, -0.13533438742160797, -0.17556548118591309, -0.18485672771930695, -0.2680685818195343, 0.0], "q01": [-0.02987122368067503, -0.06013262912631035, -0.08286409199237824, -0.05924444157630205, -0.15986866518855095, -0.15636983573436739, 0.0], "q99": [0.08832092039287087, 0.042126184627413736, 0.11311905644834042, 0.0643695573508739, 0.03941855944693088, 0.156646853685379, 1.0], "std": [0.021393608301877975, 0.01814231649041176, 0.03374375030398369, 0.01743541844189167, 0.03394376486539841, 0.04641875624656677, 0.4588589072227478]}, "num_trajectories": 104, "num_transitions": 8928, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "dobbe": {"action": {"mask": [true, true, true, true, true, true, false], "max": [38.590423583984375, 17.932697296142578, 4.843764305114746, 1.4372116327285767, 0.4340403974056244, 1.2057193517684937, 0.9998947381973267], "mean": [-0.0001120665911003016, 0.0011229600058868527, -0.00010194431524723768, -7.371398532995954e-05, -0.00067531579406932, -5.6643435527803376e-05, 0.6318281888961792], "min": [-5.700923442840576, -21.605947494506836, -123.72489929199219, -1.7229845523834229, -0.4998578727245331, -0.8867913484573364, 1.4196479014572105e-06], "q01": [-0.01119564864784479, -0.014266146533191203, -0.0071747214533388615, -0.009444301575422287, -0.03990109823644161, -0.017422311007976532, 4.003279136668425e-05], "q99": [0.01015154086053368, 0.017181577533483497, 0.007216989761218411, 0.010380979906767595, 0.03556173853576176, 0.018032474815845446, 0.9982578039169312], "std": [0.04264938458800316, 0.04428559169173241, 0.12224084138870239, 0.005388413090258837, 0.011246449314057827, 0.006287882570177317, 0.39732322096824646]}, "num_trajectories": 5208, "num_transitions": 1139911, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "fmb_dataset": {"action": {"mask": [true, true, true, true, true, true, false], "max": [1.399999976158142, 1.0, 1.399999976158142, 1.0, 1.0, 1.0, 1.0], "mean": [0.059029702097177505, -0.06476633995771408, -0.09787475317716599, 0.004325388930737972, 0.00028963794466108084, -0.04457257315516472, 0.7336440086364746], "min": [-1.399999976158142, -1.399999976158142, -1.0, -1.0, -1.0, -1.0, 0.0], "q01": [-0.8257142901420593, -1.399999976158142, -1.0, -1.0, -0.3028571307659149, -1.0, 0.0], "q99": [1.0, 0.5257142782211304, 1.0, 1.0, 0.3400000035762787, 1.0, 1.0], "std": [0.28809213638305664, 0.2820415794849396, 0.4626740515232086, 0.3266514539718628, 0.10842999070882797, 0.3440099358558655, 0.4435282051563263]}, "num_trajectories": 8612, "num_transitions": 1137459, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "fractal20220817_data": {"action": {"mask": [true, true, true, true, true, true, false], "max": [2.9984593391418457, 22.09052848815918, 2.7507524490356445, 1.570636510848999, 1.5321086645126343, 1.5691522359848022, 1.0], "mean": [0.006987582892179489, 0.006265917327255011, -0.01262515690177679, 0.04333311319351196, -0.005756212864071131, 0.0009130256366916001, 0.5354204773902893], "min": [-2.0204520225524902, -5.497899532318115, -2.031663417816162, -1.569917917251587, -1.569892168045044, -1.570419430732727, 0.0], "q01": [-0.22453527510166169, -0.14820013284683228, -0.231589707583189, -0.3517994859814644, -0.4193011274933815, -0.43643461108207704, 0.0], "q99": [0.17824687153100965, 0.14938379630446405, 0.21842354819178575, 0.5892666035890578, 0.35272657424211445, 0.44796681255102094, 1.0], "std": [0.0692116990685463, 0.05970962345600128, 0.07353084534406662, 0.15610496699810028, 0.13164450228214264, 0.14593800902366638, 0.497110515832901]}, "num_trajectories": 87212, "num_transitions": 3786400, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "furniture_bench_dataset_converted_externally_to_rlds": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.10000000149011612, 0.10000000149011612, 0.10000000149011612, 0.8651833534240723, 1.0909736156463623, 2.863185405731201, 1.0], "mean": [0.00014610752987209707, 0.0010830952087417245, 0.0006224989192560315, -0.003303206292912364, -0.0026880695950239897, 0.018242603167891502, 0.48854944109916687], "min": [-0.10495579987764359, -0.10939455777406693, -0.10000000149011612, -0.971906840801239, -1.0475432872772217, -3.06000018119812, 0.0], "q01": [-0.053988199681043625, -0.05049169331789017, -0.032499241530895236, -0.1953887003660202, -0.41674559473991396, -0.8886768388748169, 0.0], "q99": [0.05414841488003723, 0.04965164884924884, 0.060055799782276154, 0.18231668293476103, 0.39867786407470646, 0.8772023963928218, 1.0], "std": [0.01610708422958851, 0.014891477301716805, 0.014014219865202904, 0.058274295181035995, 0.11417088657617569, 0.33479776978492737, 0.49991825222969055]}, "num_trajectories": 5100, "num_transitions": 3948057, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "iamlab_cmu_pickup_insert_converted_externally_to_rlds": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.6634981632232666, 0.23428471386432648, 0.4308285415172577, 3.1415927410125732, 0.13647015392780304, 3.141592502593994, 1.0], "mean": [0.5274372696876526, 0.02858201041817665, 0.18712575733661652, 1.2339589595794678, 0.03226623684167862, -1.4199490547180176, 0.5550631880760193], "min": [0.3071657121181488, -0.29754969477653503, 0.06578229367733002, -3.1415927410125732, -0.04584203287959099, -3.141592502593994, 0.0], "q01": [0.3148897051811218, -0.20317550599575043, 0.06785467118024827, -3.140952730178833, -0.029743434861302376, -3.141091251373291, 0.0], "q99": [0.6472805738449097, 0.20846802592277527, 0.36855655312538155, 3.1409926891326903, 0.11424950212240226, 3.1410969257354737, 1.0], "std": [0.08108345419168472, 0.1116757020354271, 0.07747554779052734, 2.8737246990203857, 0.02774704433977604, 2.7678682804107666, 0.49695101380348206]}, "num_trajectories": 631, "num_transitions": 146241, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "jaco_play": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.20000000298023224, 0.20000000298023224, 0.20000000298023224, 0.0, 0.0, 0.0, 1.0], "mean": [0.0009658430935814977, -0.00580078037455678, -0.00395062193274498, 0.0, 0.0, 0.0, 0.34934908151626587], "min": [-0.20000000298023224, -0.20000000298023224, -0.20000000298023224, 0.0, 0.0, 0.0, 0.0], "q01": [-0.20000000298023224, -0.20000000298023224, -0.20000000298023224, 0.0, 0.0, 0.0, 0.0], "q99": [0.20000000298023224, 0.20000000298023224, 0.20000000298023224, 0.0, 0.0, 0.0, 1.0], "std": [0.12235074490308762, 0.09678777307271957, 0.11155334860086441, 0.0, 0.0, 0.0, 0.4768252968788147]}, "num_trajectories": 1085, "num_transitions": 77965, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "kuka": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.1697135865688324, 0.2777623236179352, 0.43710532784461975, 0.0, 0.0, 1.9684287309646606, 1.0], "mean": [-0.0004668905457947403, 0.00040138536132872105, -0.001280792523175478, 0.0, 0.0, -0.03722453489899635, 0.4131543040275574], "min": [-0.159867063164711, -0.2892282009124756, -0.2795473635196686, 0.0, 0.0, -1.9875637292861938, 0.0], "q01": [-0.06619441494345665, -0.08713878810405731, -0.15083016991615295, 0.0, 0.0, -0.5415697038173676, 0.0], "q99": [0.06601839080452929, 0.08732476785779003, 0.18168179214000715, 0.0, 0.0, 0.2923380345106127, 1.0], "std": [0.02083250693976879, 0.02915887162089348, 0.06422865390777588, 0.0, 0.0, 0.14224295318126678, 0.49086448550224304]}, "num_trajectories": 209880, "num_transitions": 2455879, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "nyu_franka_play_dataset_converted_externally_to_rlds": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.06424188613891602, 0.07027634978294373, 0.06129661202430725, 6.281067848205566, 0.1967729926109314, 0.26377415657043457, 1.0], "mean": [0.001021989737637341, -0.00012002651783404872, 0.00032894269679673016, 0.0015034361276775599, -0.002198522910475731, -0.001663230243138969, 0.7230083346366882], "min": [-0.05952230095863342, -0.07232445478439331, -0.06730806827545166, -6.278434753417969, -0.21479034423828125, -0.3627619743347168, 0.0], "q01": [-0.03199600875377655, -0.032861671447753905, -0.03368805110454559, -0.12080862045288086, -0.12175218224525451, -0.11370223641395569, 0.0], "q99": [0.03101520001888276, 0.0373908892273903, 0.03646374464035038, 0.11764093399047852, 0.1258920183777809, 0.09366151213645942, 1.0], "std": [0.01327415369451046, 0.013215910643339157, 0.012822109274566174, 0.2732451558113098, 0.057022541761398315, 0.039172880351543427, 0.44752755761146545]}, "num_trajectories": 456, "num_transitions": 44875, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "roboturk": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.39124172925949097, 0.4601028263568878, 0.4870833456516266, 1.816888689994812, 1.8240282535552979, 1.4824820756912231, 1.0], "mean": [0.0014448732836171985, -0.0015945249469950795, -0.0011753785656765103, 0.0023012510500848293, -0.0009382463176734746, -0.00011485807772260159, 0.5746025443077087], "min": [-0.6546999216079712, -0.6365841031074524, -0.4217723608016968, -1.6695482730865479, -1.8023357391357422, -1.4630827903747559, 0.0], "q01": [-0.1342635464668274, -0.19996687173843383, -0.1482972100377083, -0.20720748245716095, -0.09676413893699647, -0.18075634717941286, 0.0], "q99": [0.14956976801157001, 0.1805950567126275, 0.18841815620660796, 0.21615413755178453, 0.09457383215427405, 0.18543301910162005, 1.0], "std": [0.04935386776924133, 0.0635455846786499, 0.061164740473032, 0.09553450345993042, 0.08420111238956451, 0.06517903506755829, 0.49452081322669983]}, "num_trajectories": 1995, "num_transitions": 187507, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "stanford_hydra_dataset_converted_externally_to_rlds": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.02499854564666748, 0.02499903365969658, 0.024999922141432762, 0.24974457919597626, 0.24997030198574066, 0.24999946355819702, 1.0], "mean": [0.0007790001109242439, 0.00013707754260394722, -0.0002548607881180942, 0.0012903271708637476, -0.004751681815832853, 0.002692886395379901, 0.48855218291282654], "min": [-0.024999044835567474, -0.024999700486660004, -0.02499929815530777, -0.24993225932121277, -0.2499666064977646, -0.2499932497739792, 0.0], "q01": [-0.019992006458342076, -0.02415412735193968, -0.022941758055239916, -0.11085530579090118, -0.12024572037160397, -0.13314770206809043, 0.0], "q99": [0.022886231057345868, 0.022358838934451335, 0.02410089675337076, 0.12370114490389822, 0.11323311634361738, 0.18474749639630164, 1.0], "std": [0.008022161200642586, 0.009131459519267082, 0.009574338793754578, 0.04122216999530792, 0.0384303517639637, 0.04606688767671585, 0.49976691603660583]}, "num_trajectories": 570, "num_transitions": 358234, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "taco_play": {"action": {"mask": [true, true, true, true, true, true, false], "max": [1.4915844202041626, 2.1842432022094727, 2.6836395263671875, 5.035226821899414, 2.665864944458008, 4.250768661499023, 1.0], "mean": [-0.003845922416076064, 0.009671456180512905, 0.012780580669641495, -0.005403771996498108, -0.009606587700545788, -0.002480733208358288, 0.4263913035392761], "min": [-4.242457866668701, -3.192805051803589, -1.3371467590332031, -4.202683448791504, -2.6722638607025146, -3.3467135429382324, 0.0], "q01": [-0.7106140398979186, -1.056944659948349, -0.5878450274467468, -0.7682853937149048, -0.7180147767066956, -1.5527938604354858, 0.0], "q99": [0.6482916426658629, 1.0051310062408447, 0.9480248689651489, 0.6926478147506714, 0.6351067513227462, 1.628010264635086, 1.0], "std": [0.23254038393497467, 0.36298269033432007, 0.28692901134490967, 0.2617705166339874, 0.2438892275094986, 0.5216503143310547, 0.4946896731853485]}, "num_trajectories": 3603, "num_transitions": 237798, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "toto": {"action": {"mask": [true, true, true, true, true, true, false], "max": [0.6839867234230042, 0.4454185664653778, 0.7984078526496887, 2.120781660079956, 1.371164321899414, 1.4118704795837402, 0.0], "mean": [0.38542115688323975, 0.007769413758069277, 0.3632740378379822, -0.6652036905288696, 0.1890396922826767, 0.03298724442720413, 0.0], "min": [0.09922284632921219, -0.5180193781852722, 0.13791072368621826, -2.635117530822754, -1.0734480619430542, -1.9282547235488892, 0.0], "q01": [0.1756722891330719, -0.3077590811252594, 0.235383919775486, -2.0908505964279174, -0.6191593289375306, -0.7488683319091797, 0.0], "q99": [0.6136963081359863, 0.33704194784164443, 0.6681221985816956, 0.7422861719131538, 0.7955395007133507, 0.740464625358582, 0.0], "std": [0.12211652100086212, 0.19378550350666046, 0.10178236663341522, 0.5725259184837341, 0.29884573817253113, 0.3259911835193634, 0.0]}, "num_trajectories": 1003, "num_transitions": 325699, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "ucsd_kitchen_dataset_converted_externally_to_rlds": {"action": {"mask": [true, true, true, true, true, true, false], "max": [678.0, 400.0, 507.0, 180.00001525878906, 6.000013828277588, 116.99998474121094, 1.0], "mean": [410.37567138671875, 116.9518814086914, 192.35032653808594, -121.22441864013672, -33.84893035888672, 50.016136169433594, 0.741813600063324], "min": [172.0, -166.0, -99.99999237060547, -180.00001525878906, -89.0, -96.00010681152344, 0.0], "q01": [200.00001052856445, -102.31004211425781, -94.99993370056153, -180.00001525878906, -88.00001525878906, -38.999977111816406, 0.0], "q99": [637.0, 368.30999999999995, 493.0, 180.00001525878906, 0.999983012676239, 105.00001525878906, 1.0], "std": [122.81494903564453, 108.8009033203125, 130.303466796875, 116.28205108642578, 27.621843338012695, 41.02094650268555, 0.43763357400894165]}, "num_trajectories": 150, "num_transitions": 3970, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "utaustin_mutex": {"action": {"mask": [true, true, true, true, true, true, false], "max": [1.0, 1.0, 1.0, 0.375, 0.375, 0.375, 1.0], "mean": [0.06176406890153885, -0.005005486309528351, 0.10216785222291946, -0.03314131125807762, 0.013895004987716675, -0.011317633092403412, 0.5038976669311523], "min": [-1.0, -1.0, -1.0, -0.375, -0.375, -0.375, 0.0], "q01": [-0.4285714328289032, -0.9800000190734863, -0.5571428537368774, -0.375, -0.15642857551574707, -0.335357129573822, 0.0], "q99": [0.5914285778999329, 0.9714285731315613, 1.0, 0.3278571367263794, 0.207857146859169, 0.25607141852378845, 1.0], "std": [0.1875014752149582, 0.4468473494052887, 0.3792876601219177, 0.14097853004932404, 0.06453701853752136, 0.11765272170305252, 0.501045286655426]}, "num_trajectories": 1500, "num_transitions": 361883, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}, "viola": {"action": {"mask": [true, true, true, true, true, true, false], "max": [1.0, 1.0, 1.0, 0.375, 0.36321428418159485, 0.375, 1.0], "mean": [0.04761844128370285, -0.029204415157437325, 0.05586736649274826, -0.002618510741740465, 0.006867344491183758, -0.01682133786380291, 0.7323777675628662], "min": [-1.0, -1.0, -1.0, -0.375, -0.375, -0.375, 0.0], "q01": [-0.9628571271896362, -1.0, -1.0, -0.26249998807907104, -0.21321429312229156, -0.3385714292526245, 0.0], "q99": [0.9114285707473755, 0.868571400642395, 1.0, 0.2817857265472412, 0.2239285707473755, 0.3557142913341522, 1.0], "std": [0.39157867431640625, 0.4076525568962097, 0.40077948570251465, 0.10023996233940125, 0.0844319611787796, 0.10375042259693146, 0.44260647892951965]}, "num_trajectories": 150, "num_transitions": 76324, "proprio": {"max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}}, "output_projector_states": false, "pad_to_multiple_of": 64, "pad_token_id": 32000, "text_config": {"attention_bias": false, "attention_dropout": 0.0, "head_dim": 128, "hidden_act": "silu", "hidden_size": 4096, "initializer_range": 0.02, "intermediate_size": 11008, "max_position_embeddings": 2048, "mlp_bias": false, "model_type": "llama", "num_attention_heads": 32, "num_hidden_layers": 32, "num_key_value_heads": 32, "pad_token_id": 32000, "pretraining_tp": 1, "rms_norm_eps": 1e-06, "rope_scaling": null, "rope_theta": 10000.0, "torch_dtype": "bfloat16", "use_cache": true, "vocab_size": 32064}, "timm_model_ids": ["vit_large_patch14_reg4_dinov2.lvd142m", "vit_so400m_patch14_siglip_224"], "timm_override_act_layers": [null, null], "torch_dtype": "bfloat16", "transformers_version": "4.51.3", "use_fused_vision_backbone": true, "vision_backbone_id": "dinosiglip-vit-so-224px"}